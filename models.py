"""
Data models for the AI Companion System.
Defines the structure for memory, conversations, and user data.
Optimized for high performance and efficient memory usage.
"""

from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any, Union, Set
from enum import Enum
from pydantic import BaseModel, Field, ConfigDict
import json
import hashlib
from functools import lru_cache
import asyncio

def utc_now() -> datetime:
    """Get current UTC time with timezone awareness."""
    return datetime.now(timezone.utc)

@lru_cache(maxsize=1000)
def cached_emotion_weights() -> Dict[str, float]:
    """Cached emotion weights for performance."""
    return {
        "joy": 0.8, "sadness": 0.9, "anger": 0.7, "fear": 0.8,
        "surprise": 0.6, "excitement": 0.7, "anxiety": 0.8,
        "contentment": 0.6, "stress": 0.9, "loneliness": 0.9
    }

class MemoryType(str, Enum):
    """Types of memory in the dual-memory architecture."""
    PERSONAL = "personal"
    UNIVERSAL = "universal"

class EmotionType(str, Enum):
    """Enhanced emotion categories for advanced emotional intelligence."""
    # Primary emotions
    JOY = "joy"
    SADNESS = "sadness"
    ANGER = "anger"
    FEAR = "fear"
    SURPRISE = "surprise"
    DISGUST = "disgust"
    NEUTRAL = "neutral"

    # Secondary emotions
    EXCITEMENT = "excitement"
    ANXIETY = "anxiety"
    CONTENTMENT = "contentment"
    FRUSTRATION = "frustration"
    HOPE = "hope"
    DISAPPOINTMENT = "disappointment"
    GRATITUDE = "gratitude"
    LONELINESS = "loneliness"
    CONFUSION = "confusion"
    PRIDE = "pride"
    SHAME = "shame"
    LOVE = "love"
    STRESS = "stress"
    RELIEF = "relief"
    CURIOSITY = "curiosity"
    BOREDOM = "boredom"
    OVERWHELM = "overwhelm"

class InteractionType(str, Enum):
    """Enhanced types of user interactions."""
    CONVERSATION = "conversation"
    PREFERENCE = "preference"
    EMOTION = "emotion"
    ROUTINE = "routine"
    GOAL = "goal"
    INTEREST = "interest"
    FEEDBACK = "feedback"
    SUPPORT_SEEKING = "support_seeking"
    CELEBRATION = "celebration"
    VENTING = "venting"
    ADVICE_REQUEST = "advice_request"
    SHARING = "sharing"
    REFLECTION = "reflection"
    CRISIS = "crisis"

class MemoryEntry(BaseModel):
    """Optimized base memory entry structure with performance enhancements."""

    id: str = Field(..., description="Unique identifier for the memory entry")
    user_id: str = Field(..., description="User identifier (empty for universal memory)")
    memory_type: MemoryType = Field(..., description="Type of memory (personal or universal)")
    interaction_type: InteractionType = Field(..., description="Type of interaction")
    content: str = Field(..., description="The actual memory content")
    context: Dict[str, Any] = Field(default_factory=dict, description="Additional context")
    emotion: Optional[EmotionType] = Field(None, description="Associated emotion")
    confidence: float = Field(1.0, ge=0.0, le=1.0, description="Confidence in this memory")
    importance: float = Field(0.5, ge=0.0, le=1.0, description="Importance weight")
    created_at: datetime = Field(default_factory=utc_now)
    last_accessed: datetime = Field(default_factory=utc_now)
    access_count: int = Field(0, description="Number of times this memory was accessed")

    # Performance optimization fields
    _content_hash: Optional[str] = Field(None, description="Cached content hash for deduplication")
    _embedding: Optional[List[float]] = Field(None, description="Cached embedding vector")
    _search_keywords: Optional[List[str]] = Field(None, description="Cached search keywords")

    def get_content_hash(self) -> str:
        """Get or compute content hash for deduplication."""
        if not self._content_hash:
            self._content_hash = hashlib.md5(self.content.encode()).hexdigest()
        return self._content_hash

    def get_search_keywords(self) -> List[str]:
        """Get or compute search keywords for fast retrieval."""
        if not self._search_keywords:
            # Simple keyword extraction (can be enhanced with NLP)
            words = self.content.lower().split()
            self._search_keywords = [w for w in words if len(w) > 3][:10]  # Top 10 keywords
        return self._search_keywords

    model_config = {
        "arbitrary_types_allowed": True,
        "validate_assignment": False,
        "json_encoders": {
            datetime: lambda v: v.isoformat()
        }
    }

class PersonalMemory(MemoryEntry):
    """Personal memory entry - strictly isolated per user."""
    user_id: str = Field(..., description="User identifier (required for personal memory)")
    memory_type: MemoryType = Field(MemoryType.PERSONAL, description="Always personal")
    
    # Personal-specific fields
    privacy_level: str = Field("private", description="Privacy level of this memory")
    relationship_context: Optional[str] = Field(None, description="Relationship context")
    personal_goals: List[str] = Field(default_factory=list, description="Related personal goals")

class UniversalMemory(MemoryEntry):
    """Universal memory entry - shared across all users."""
    user_id: str = Field("", description="Empty for universal memory")
    memory_type: MemoryType = Field(MemoryType.UNIVERSAL, description="Always universal")
    
    # Universal-specific fields
    topic_tags: List[str] = Field(default_factory=list, description="Topic tags for categorization")
    global_relevance: float = Field(0.5, ge=0.0, le=1.0, description="Global relevance score")
    source_count: int = Field(1, description="Number of users who contributed to this memory")

class ConversationMessage(BaseModel):
    """Individual message in a conversation."""
    id: str = Field(..., description="Unique message identifier")
    user_id: str = Field(..., description="User identifier")
    role: str = Field(..., description="Role: 'user' or 'assistant'")
    content: str = Field(..., description="Message content")
    timestamp: datetime = Field(default_factory=utc_now)
    emotion: Optional[EmotionType] = Field(None, description="Detected emotion")
    confidence: float = Field(1.0, ge=0.0, le=1.0, description="Confidence in emotion detection")
    context: Dict[str, Any] = Field(default_factory=dict, description="Additional context")

class Conversation(BaseModel):
    """Complete conversation session."""
    id: str = Field(..., description="Unique conversation identifier")
    user_id: str = Field(..., description="User identifier")
    messages: List[ConversationMessage] = Field(default_factory=list, description="Conversation messages")
    start_time: datetime = Field(default_factory=utc_now)
    end_time: Optional[datetime] = Field(None, description="Conversation end time")
    summary: Optional[str] = Field(None, description="Conversation summary")
    topics: List[str] = Field(default_factory=list, description="Topics discussed")
    emotions: List[EmotionType] = Field(default_factory=list, description="Emotions detected")

    model_config = {
        "json_encoders": {
            datetime: lambda v: v.isoformat()
        }
    }

class UserProfile(BaseModel):
    """User profile and preferences."""
    user_id: str = Field(..., description="Unique user identifier")
    name: Optional[str] = Field(None, description="User's preferred name")
    preferences: Dict[str, Any] = Field(default_factory=dict, description="User preferences")
    interests: List[str] = Field(default_factory=list, description="User interests")
    communication_style: Dict[str, float] = Field(default_factory=dict, description="Communication style preferences")
    emotional_patterns: Dict[EmotionType, float] = Field(default_factory=dict, description="Emotional patterns")
    routines: List[str] = Field(default_factory=list, description="User routines")
    goals: List[str] = Field(default_factory=list, description="User goals")
    created_at: datetime = Field(default_factory=utc_now)
    last_interaction: datetime = Field(default_factory=utc_now)
    interaction_count: int = Field(0, description="Total number of interactions")

    model_config = {
        "json_encoders": {
            datetime: lambda v: v.isoformat()
        }
    }

class LearningInsight(BaseModel):
    """Insights derived from user interactions."""
    id: str = Field(..., description="Unique insight identifier")
    user_id: str = Field(..., description="User identifier")
    insight_type: str = Field(..., description="Type of insight")
    content: str = Field(..., description="Insight content")
    confidence: float = Field(1.0, ge=0.0, le=1.0, description="Confidence in the insight")
    sources: List[str] = Field(default_factory=list, description="Source memory IDs")
    created_at: datetime = Field(default_factory=utc_now)
    last_updated: datetime = Field(default_factory=utc_now)

    model_config = {
        "json_encoders": {
            datetime: lambda v: v.isoformat()
        }
    }

class ContextualMemory(BaseModel):
    """Contextual memory for conversation grounding."""
    conversation_id: str = Field(..., description="Associated conversation")
    user_id: str = Field(..., description="User identifier")
    relevant_memories: List[MemoryEntry] = Field(default_factory=list, description="Relevant memories")
    emotional_context: Dict[EmotionType, float] = Field(default_factory=dict, description="Emotional context")
    topic_context: List[str] = Field(default_factory=list, description="Topic context")
    relationship_context: Dict[str, Any] = Field(default_factory=dict, description="Relationship context")
    created_at: datetime = Field(default_factory=utc_now)

    model_config = {
        "json_encoders": {
            datetime: lambda v: v.isoformat()
        }
    }

class EmotionalState(BaseModel):
    """Advanced emotional state tracking."""
    primary_emotion: EmotionType = Field(EmotionType.NEUTRAL, description="Primary detected emotion")
    secondary_emotions: Dict[EmotionType, float] = Field(default_factory=dict, description="Secondary emotions with weights")
    intensity: float = Field(0.5, ge=0.0, le=1.0, description="Emotional intensity")
    confidence: float = Field(0.5, ge=0.0, le=1.0, description="Confidence in emotion detection")
    valence: float = Field(0.0, ge=-1.0, le=1.0, description="Emotional valence (negative to positive)")
    arousal: float = Field(0.0, ge=-1.0, le=1.0, description="Emotional arousal (calm to excited)")
    stability: float = Field(0.5, ge=0.0, le=1.0, description="Emotional stability")
    context_factors: List[str] = Field(default_factory=list, description="Contextual factors affecting emotion")
    timestamp: datetime = Field(default_factory=utc_now)

    def get_dominant_emotions(self, threshold: float = 0.3) -> List[EmotionType]:
        """Get emotions above threshold."""
        emotions = [self.primary_emotion]
        emotions.extend([
            emotion for emotion, weight in self.secondary_emotions.items()
            if weight >= threshold
        ])
        return emotions

class EmpathyModel(BaseModel):
    """Model for empathetic response generation."""
    user_emotional_state: EmotionalState = Field(..., description="Current user emotional state")
    emotional_history: List[EmotionalState] = Field(default_factory=list, description="Recent emotional history")
    empathy_level: float = Field(0.7, ge=0.0, le=1.0, description="Empathy level for response")
    response_strategy: str = Field("supportive", description="Response strategy (supportive, validating, encouraging, etc.)")
    emotional_mirroring: float = Field(0.3, ge=0.0, le=1.0, description="Level of emotional mirroring")
    comfort_techniques: List[str] = Field(default_factory=list, description="Applicable comfort techniques")

    def should_provide_support(self) -> bool:
        """Determine if user needs emotional support."""
        negative_emotions = [
            EmotionType.SADNESS, EmotionType.ANGER, EmotionType.FEAR,
            EmotionType.ANXIETY, EmotionType.FRUSTRATION, EmotionType.LONELINESS,
            EmotionType.DISAPPOINTMENT, EmotionType.SHAME, EmotionType.STRESS,
            EmotionType.OVERWHELM
        ]
        return (
            self.user_emotional_state.primary_emotion in negative_emotions or
            self.user_emotional_state.valence < -0.3 or
            any(emotion in negative_emotions for emotion in self.user_emotional_state.get_dominant_emotions())
        )

class EmotionalMemory(BaseModel):
    """Memory entry with enhanced emotional context."""
    base_memory: MemoryEntry = Field(..., description="Base memory entry")
    emotional_state: EmotionalState = Field(..., description="Emotional state when memory was created")
    emotional_triggers: List[str] = Field(default_factory=list, description="Emotional triggers associated with memory")
    emotional_resolution: Optional[str] = Field(None, description="How the emotional situation was resolved")
    support_provided: List[str] = Field(default_factory=list, description="Types of support provided")
    outcome_emotion: Optional[EmotionType] = Field(None, description="Resulting emotion after interaction")

    model_config = {
        "json_encoders": {
            datetime: lambda v: v.isoformat()
        }
    }

# Utility functions for memory management
def create_memory_id(user_id: str, memory_type: MemoryType, content_hash: str) -> str:
    """Create a unique memory ID."""
    timestamp = utc_now().strftime("%Y%m%d_%H%M%S")
    return f"{memory_type.value}_{user_id}_{content_hash}_{timestamp}"

def calculate_memory_importance(
    emotion: Optional[EmotionType],
    interaction_type: InteractionType,
    user_frequency: int,
    content_length: int
) -> float:
    """Calculate memory importance based on various factors."""
    importance = 0.5  # Base importance
    
    # Emotion factor
    if emotion:
        emotion_weights = {
            EmotionType.JOY: 0.8,
            EmotionType.SADNESS: 0.9,
            EmotionType.ANGER: 0.7,
            EmotionType.FEAR: 0.8,
            EmotionType.SURPRISE: 0.6,
            EmotionType.EXCITEMENT: 0.7,
            EmotionType.ANXIETY: 0.8,
            EmotionType.CONTENTMENT: 0.6
        }
        importance += emotion_weights.get(emotion, 0.5) * 0.2
    
    # Interaction type factor
    type_weights = {
        InteractionType.EMOTION: 0.9,
        InteractionType.PREFERENCE: 0.8,
        InteractionType.GOAL: 0.9,
        InteractionType.ROUTINE: 0.6,
        InteractionType.INTEREST: 0.7,
        InteractionType.FEEDBACK: 0.8
    }
    importance += type_weights.get(interaction_type, 0.5) * 0.2
    
    # User frequency factor (normalized)
    frequency_factor = min(user_frequency / 100, 1.0) * 0.1
    importance += frequency_factor
    
    # Content length factor (normalized)
    length_factor = min(content_length / 1000, 1.0) * 0.1
    importance += length_factor
    
    return min(importance, 1.0) 