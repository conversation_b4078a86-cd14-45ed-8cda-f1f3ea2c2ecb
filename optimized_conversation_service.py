"""
High-Performance Conversation Service for AI Companion System.
Optimized for ultra-fast response times and efficient resource usage.
"""

import asyncio
import uuid
import logging
import time
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
from functools import lru_cache
import json

from models import (
    UserProfile, EmotionType, InteractionType, MemoryType, ContextualMemory,
    EmotionalState, EmpathyModel, EmotionalMemory, MemoryEntry
)
from optimized_memory_service import HighPerformanceMemoryService
from gemini_service import GeminiService
from emotional_intelligence_service import EmotionalIntelligenceService
from config import settings

logger = logging.getLogger(__name__)

@dataclass
class FastConversationContext:
    """Optimized conversation context with minimal overhead."""
    user_id: str
    conversation_id: str
    user_profile: Optional[UserProfile]
    recent_messages: deque  # Limited size for performance
    emotional_state: EmotionalState
    current_topics: List[str]
    last_activity: float  # Unix timestamp
    response_cache: Dict[str, str]  # Cache recent responses

class OptimizedConversationService:
    """Ultra-fast conversation service with intelligent caching and async operations."""
    
    def __init__(self):
        """Initialize optimized conversation service."""
        self.memory_service = HighPerformanceMemoryService()
        self.gemini_service = GeminiService()
        self.emotional_intelligence = EmotionalIntelligenceService(self.gemini_service)
        
        # High-performance conversation management
        self.active_conversations: Dict[str, FastConversationContext] = {}
        self.user_conversations: Dict[str, List[str]] = defaultdict(list)
        
        # Response caching for common patterns
        self.response_cache = {}
        self.cache_hits = 0
        self.cache_misses = 0
        
        # Async task queue for background processing
        self.background_tasks = asyncio.Queue()
        
        # Performance settings
        self.max_recent_messages = 10
        self.max_active_conversations = 1000
        self.response_cache_size = 5000
        
        # Start background task processor
        asyncio.create_task(self._process_background_tasks())
    
    async def _process_background_tasks(self):
        """Process background tasks asynchronously."""
        while True:
            try:
                task = await self.background_tasks.get()
                await task()
                self.background_tasks.task_done()
            except Exception as e:
                logger.error(f"Background task error: {e}")
    
    def _generate_response_cache_key(self, user_id: str, message: str, emotional_state: EmotionalState) -> str:
        """Generate cache key for response caching."""
        emotion_key = f"{emotional_state.primary_emotion.value}_{emotional_state.intensity:.1f}"
        message_hash = hash(message.lower().strip()) % 10000  # Simple hash
        return f"{user_id}:{emotion_key}:{message_hash}"
    
    async def start_conversation_fast(self, user_id: str, user_name: Optional[str] = None) -> str:
        """Start conversation with minimal latency."""
        conversation_id = str(uuid.uuid4())
        
        # Check if user profile is cached
        user_profile = await self._get_user_profile_fast(user_id, user_name)
        
        # Create optimized context
        context = FastConversationContext(
            user_id=user_id,
            conversation_id=conversation_id,
            user_profile=user_profile,
            recent_messages=deque(maxlen=self.max_recent_messages),
            emotional_state=EmotionalState(primary_emotion=EmotionType.NEUTRAL),
            current_topics=[],
            last_activity=time.time(),
            response_cache={}
        )
        
        # Manage memory usage
        if len(self.active_conversations) >= self.max_active_conversations:
            await self._cleanup_inactive_conversations()
        
        self.active_conversations[conversation_id] = context
        self.user_conversations[user_id].append(conversation_id)
        
        return conversation_id
    
    async def _get_user_profile_fast(self, user_id: str, user_name: Optional[str] = None) -> Optional[UserProfile]:
        """Get user profile with caching optimization."""
        # Try memory service cache first
        profile = self.memory_service.user_profile_cache.get(user_id)
        if profile:
            return profile
        
        # Load from storage in background if not found
        if not profile:
            asyncio.create_task(self._load_user_profile_background(user_id, user_name))
            
        return None
    
    async def _load_user_profile_background(self, user_id: str, user_name: Optional[str]):
        """Load user profile in background."""
        try:
            # This would load from database
            # For now, create a minimal profile
            profile = UserProfile(
                user_id=user_id,
                name=user_name,
                preferences={},
                interests=[],
                communication_style={},
                emotional_patterns={},
                routines=[],
                goals=[]
            )
            self.memory_service.user_profile_cache[user_id] = profile
        except Exception as e:
            logger.error(f"Error loading user profile: {e}")
    
    async def process_message_fast(
        self,
        conversation_id: str,
        user_message: str,
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Process message with maximum speed optimization."""
        start_time = time.time()
        
        try:
            # Get conversation context
            context = self.active_conversations.get(conversation_id)
            if not context:
                return {"error": "Conversation not found", "response": "I'm sorry, I don't have the context for this conversation."}
            
            context.last_activity = time.time()
            
            # Quick emotion detection (simplified for speed)
            emotional_state = await self._detect_emotion_fast(user_message, context.emotional_state)
            context.emotional_state = emotional_state
            
            # Check response cache
            cache_key = self._generate_response_cache_key(context.user_id, user_message, emotional_state)
            if cache_key in self.response_cache:
                self.cache_hits += 1
                cached_response = self.response_cache[cache_key]
                
                # Add to recent messages
                context.recent_messages.append({
                    "role": "user",
                    "content": user_message,
                    "timestamp": time.time(),
                    "emotion": emotional_state.primary_emotion.value
                })
                context.recent_messages.append({
                    "role": "assistant", 
                    "content": cached_response,
                    "timestamp": time.time()
                })
                
                processing_time = time.time() - start_time
                return {
                    "response": cached_response,
                    "emotion": emotional_state.primary_emotion.value,
                    "confidence": emotional_state.confidence,
                    "processing_time": processing_time,
                    "cached": True
                }
            
            self.cache_misses += 1
            
            # Generate response using optimized pipeline
            response = await self._generate_response_fast(context, user_message, emotional_state)
            
            # Cache the response
            if len(self.response_cache) < self.response_cache_size:
                self.response_cache[cache_key] = response
            
            # Add to recent messages
            context.recent_messages.append({
                "role": "user",
                "content": user_message,
                "timestamp": time.time(),
                "emotion": emotional_state.primary_emotion.value
            })
            context.recent_messages.append({
                "role": "assistant",
                "content": response,
                "timestamp": time.time()
            })
            
            # Background memory storage (non-blocking)
            asyncio.create_task(self._store_interaction_background(context, user_message, response, emotional_state))
            
            processing_time = time.time() - start_time
            
            return {
                "response": response,
                "emotion": emotional_state.primary_emotion.value,
                "confidence": emotional_state.confidence,
                "processing_time": processing_time,
                "cached": False
            }
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            processing_time = time.time() - start_time
            return {
                "error": str(e),
                "response": "I'm experiencing some technical difficulties. Please try again.",
                "processing_time": processing_time
            }
    
    async def _detect_emotion_fast(self, message: str, previous_state: EmotionalState) -> EmotionalState:
        """Fast emotion detection with caching."""
        # Simplified emotion detection for speed
        # In production, this could use a lightweight model or cache
        
        emotion_keywords = {
            EmotionType.JOY: ["happy", "excited", "great", "wonderful", "amazing"],
            EmotionType.SADNESS: ["sad", "depressed", "down", "upset", "crying"],
            EmotionType.ANGER: ["angry", "mad", "furious", "annoyed", "frustrated"],
            EmotionType.ANXIETY: ["anxious", "worried", "nervous", "stressed", "panic"],
            EmotionType.FEAR: ["scared", "afraid", "terrified", "frightened"],
        }
        
        message_lower = message.lower()
        detected_emotions = {}
        
        for emotion, keywords in emotion_keywords.items():
            score = sum(1 for keyword in keywords if keyword in message_lower)
            if score > 0:
                detected_emotions[emotion] = score / len(keywords)
        
        if detected_emotions:
            primary_emotion = max(detected_emotions, key=detected_emotions.get)
            confidence = detected_emotions[primary_emotion]
        else:
            primary_emotion = EmotionType.NEUTRAL
            confidence = 0.5
        
        return EmotionalState(
            primary_emotion=primary_emotion,
            confidence=confidence,
            intensity=min(confidence * 1.5, 1.0),
            valence=0.5 if primary_emotion == EmotionType.JOY else -0.3 if primary_emotion in [EmotionType.SADNESS, EmotionType.ANGER] else 0.0,
            arousal=confidence if primary_emotion in [EmotionType.ANGER, EmotionType.ANXIETY] else 0.3
        )
    
    async def _generate_response_fast(
        self, 
        context: FastConversationContext, 
        user_message: str, 
        emotional_state: EmotionalState
    ) -> str:
        """Generate response with speed optimization."""
        
        # Build minimal context for Gemini
        recent_context = []
        for msg in list(context.recent_messages)[-4:]:  # Last 4 messages only
            recent_context.append(f"{msg['role']}: {msg['content']}")
        
        context_str = "\n".join(recent_context) if recent_context else ""
        
        # Create optimized prompt
        prompt = self._create_fast_prompt(user_message, emotional_state, context_str, context.user_profile)
        
        # Generate response with timeout
        try:
            response = await asyncio.wait_for(
                self.gemini_service.generate_response_async(prompt),
                timeout=5.0  # 5 second timeout
            )
            return response.strip()
        except asyncio.TimeoutError:
            logger.warning("Gemini response timeout, using fallback")
            return self._get_fallback_response(emotional_state)
        except Exception as e:
            logger.error(f"Gemini error: {e}")
            return self._get_fallback_response(emotional_state)
    
    def _create_fast_prompt(
        self, 
        user_message: str, 
        emotional_state: EmotionalState, 
        context: str,
        user_profile: Optional[UserProfile]
    ) -> str:
        """Create optimized prompt for fast response generation."""
        
        emotion_guidance = {
            EmotionType.SADNESS: "Be empathetic and supportive. Acknowledge their feelings.",
            EmotionType.ANXIETY: "Be calming and reassuring. Offer gentle support.",
            EmotionType.ANGER: "Be understanding and help them process their feelings.",
            EmotionType.JOY: "Share in their happiness and be enthusiastic.",
            EmotionType.NEUTRAL: "Be friendly and engaging."
        }
        
        guidance = emotion_guidance.get(emotional_state.primary_emotion, "Be helpful and friendly.")
        
        prompt = f"""You are a caring AI companion. {guidance}

Recent conversation:
{context}

User's current message: {user_message}
Detected emotion: {emotional_state.primary_emotion.value} (confidence: {emotional_state.confidence:.2f})

Respond naturally and empathetically in 1-2 sentences. Be conversational and human-like."""
        
        return prompt
    
    def _get_fallback_response(self, emotional_state: EmotionalState) -> str:
        """Get fallback response for errors."""
        fallback_responses = {
            EmotionType.SADNESS: "I can hear that you're going through a tough time. I'm here to listen and support you.",
            EmotionType.ANXIETY: "It sounds like you're feeling anxious. Take a deep breath - you're not alone in this.",
            EmotionType.ANGER: "I understand you're feeling frustrated. It's okay to feel this way.",
            EmotionType.JOY: "I love hearing the happiness in your message! That's wonderful.",
            EmotionType.NEUTRAL: "I'm here and listening. What would you like to talk about?"
        }
        
        return fallback_responses.get(
            emotional_state.primary_emotion, 
            "I'm here to chat with you. How are you feeling today?"
        )
    
    async def _store_interaction_background(
        self, 
        context: FastConversationContext, 
        user_message: str, 
        response: str, 
        emotional_state: EmotionalState
    ):
        """Store interaction in background for performance."""
        try:
            # Create memory entry
            memory = MemoryEntry(
                id=str(uuid.uuid4()),
                user_id=context.user_id,
                memory_type=MemoryType.PERSONAL,
                interaction_type=InteractionType.CONVERSATION,
                content=f"User: {user_message}\nAssistant: {response}",
                emotion=emotional_state.primary_emotion,
                confidence=emotional_state.confidence,
                importance=0.6  # Default importance
            )
            
            # Store asynchronously
            await self.memory_service.store_memory_fast(memory)
            
        except Exception as e:
            logger.error(f"Error storing interaction: {e}")
    
    async def _cleanup_inactive_conversations(self):
        """Clean up inactive conversations to manage memory."""
        current_time = time.time()
        inactive_threshold = 3600  # 1 hour
        
        inactive_conversations = [
            conv_id for conv_id, context in self.active_conversations.items()
            if current_time - context.last_activity > inactive_threshold
        ]
        
        for conv_id in inactive_conversations:
            context = self.active_conversations.pop(conv_id)
            # Remove from user conversations list
            if context.user_id in self.user_conversations:
                self.user_conversations[context.user_id] = [
                    cid for cid in self.user_conversations[context.user_id] if cid != conv_id
                ]
        
        logger.info(f"Cleaned up {len(inactive_conversations)} inactive conversations")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        total_requests = self.cache_hits + self.cache_misses
        hit_rate = self.cache_hits / total_requests if total_requests > 0 else 0
        
        return {
            "active_conversations": len(self.active_conversations),
            "response_cache_size": len(self.response_cache),
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "cache_hit_rate": hit_rate,
            "memory_stats": self.memory_service.get_cache_stats()
        }
