2025-07-11 12:51:25,040 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-11 12:51:25,042 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("memories")
2025-07-11 12:51:25,042 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 12:51:25,058 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("user_profiles")
2025-07-11 12:51:25,066 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 12:51:25,073 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("conversations")
2025-07-11 12:51:25,073 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-11 12:51:25,073 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 12:51:25,078 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("memories")
2025-07-11 12:51:25,079 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 12:51:25,079 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-11 12:51:25,088 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("user_profiles")
2025-07-11 12:51:25,089 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 12:51:25,091 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("conversations")
2025-07-11 12:51:25,092 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 12:51:25,093 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-11 12:51:25,120 - storage_service - INFO - Redis connection established
2025-07-11 12:51:25,120 - storage_service - INFO - Redis connection established
2025-07-11 12:51:25,132 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-11 12:51:25,134 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("memories")
2025-07-11 12:51:25,134 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 12:51:25,140 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("user_profiles")
2025-07-11 12:51:25,140 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 12:51:25,143 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("conversations")
2025-07-11 12:51:25,143 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 12:51:25,144 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-11 12:51:25,147 - storage_service - INFO - Redis connection established
2025-07-11 12:51:25,296 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-11 12:51:25,297 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("memories")
2025-07-11 12:51:25,297 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 12:51:25,308 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("user_profiles")
2025-07-11 12:51:25,309 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 12:51:25,310 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("conversations")
2025-07-11 12:51:25,312 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 12:51:25,313 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-11 12:51:25,319 - storage_service - INFO - Redis connection established
2025-07-11 12:51:25,326 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-11 12:51:25,326 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("memories")
2025-07-11 12:51:25,328 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 12:51:25,329 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("user_profiles")
2025-07-11 12:51:25,330 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 12:51:25,330 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("conversations")
2025-07-11 12:51:25,332 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 12:51:25,333 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-11 12:51:25,339 - storage_service - INFO - Redis connection established
2025-07-11 12:51:25,764 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-5' coro=<HighPerformanceMemoryService._initialize_async() running at C:\Users\<USER>\Downloads\mandy\optimized_memory_service.py:71> wait_for=<Future finished result=None>>
2025-07-11 12:51:25,765 - optimized_memory_service - WARNING - Redis not available: no running event loop
2025-07-11 12:51:25,766 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-6' coro=<HighPerformanceMemoryService._initialize_async() running at C:\Users\<USER>\Downloads\mandy\optimized_memory_service.py:71> wait_for=<Future finished result=None>>
2025-07-11 12:51:25,767 - optimized_memory_service - WARNING - Redis not available: no running event loop
2025-07-11 12:51:25,767 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-7' coro=<OptimizedGeminiService._process_batches() running at C:\Users\<USER>\Downloads\mandy\gemini_service.py:56> wait_for=<Future finished result=None>>
2025-07-11 12:51:25,769 - __main__ - INFO - Performance stats: {'startup_time': 0.7014963626861572, 'system_ready': True, 'active_conversations': 0, 'response_cache_size': 0, 'cache_hits': 0, 'cache_misses': 0, 'cache_hit_rate': 0, 'memory_stats': {'cache_hits': 0, 'cache_misses': 0, 'hit_rate': 0, 'memory_cache_size': 0, 'embedding_cache_size': 0, 'search_index_size': 0}}
2025-07-11 12:51:25,774 - optimized_gradio_interface - INFO - Launching optimized AI Companion interface on port 7860
2025-07-11 12:51:27,163 - httpx - INFO - HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-07-11 12:51:28,662 - httpx - INFO - HTTP Request: GET http://localhost:7860/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-07-11 12:51:30,738 - httpx - INFO - HTTP Request: HEAD http://localhost:7860/ "HTTP/1.1 200 OK"
2025-07-11 12:51:46,257 - optimized_conversation_service - WARNING - Gemini response timeout, using fallback
2025-07-11 12:51:46,258 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-58' coro=<HighPerformanceMemoryService._store_memory_db() running at C:\Users\<USER>\Downloads\mandy\optimized_memory_service.py:181>>
2025-07-11 12:51:46,263 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-59' coro=<HighPerformanceMemoryService._store_memory_redis() running at C:\Users\<USER>\Downloads\mandy\optimized_memory_service.py:200>>
2025-07-11 12:51:55,127 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-11 12:51:55,127 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("memories")
2025-07-11 12:51:55,128 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 12:51:55,129 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("user_profiles")
2025-07-11 12:51:55,129 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 12:51:55,129 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("conversations")
2025-07-11 12:51:55,130 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 12:51:55,130 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-11 12:51:55,133 - storage_service - INFO - Redis connection established
2025-07-11 12:52:25,136 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-11 12:52:25,137 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("memories")
2025-07-11 12:52:25,137 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 12:52:25,138 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("user_profiles")
2025-07-11 12:52:25,140 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 12:52:25,140 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("conversations")
2025-07-11 12:52:25,140 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 12:52:25,141 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-11 12:52:25,145 - storage_service - INFO - Redis connection established
2025-07-11 12:52:29,047 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-73' coro=<HighPerformanceMemoryService._store_memory_db() running at C:\Users\<USER>\Downloads\mandy\optimized_memory_service.py:181>>
2025-07-11 12:52:29,049 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-74' coro=<HighPerformanceMemoryService._store_memory_redis() running at C:\Users\<USER>\Downloads\mandy\optimized_memory_service.py:200>>
